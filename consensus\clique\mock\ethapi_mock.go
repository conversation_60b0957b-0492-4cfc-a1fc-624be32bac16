// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/ethereum/go-ethereum/consensus/clique (interfaces: EthAPI)

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	types "github.com/ethereum/go-ethereum/core/types"
	rpc "github.com/ethereum/go-ethereum/rpc"
	gomock "github.com/golang/mock/gomock"
)

// MockEthAPI is a mock of EthAPI interface.
type MockEthAPI struct {
	ctrl     *gomock.Controller
	recorder *MockEthAPIMockRecorder
}

// MockEthAPIMockRecorder is the mock recorder for MockEthAPI.
type MockEthAPIMockRecorder struct {
	mock *MockEthAPI
}

// NewMockEthAPI creates a new mock instance.
func NewMockEthAPI(ctrl *gomock.Controller) *MockEthAPI {
	mock := &MockEthAPI{ctrl: ctrl}
	mock.recorder = &MockEthAPIMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockEthAPI) EXPECT() *MockEthAPIMockRecorder {
	return m.recorder
}

// GetHeaderTypeByNumber mocks base method.
func (m *MockEthAPI) GetHeaderTypeByNumber(arg0 context.Context, arg1 rpc.BlockNumber) (*types.Header, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetHeaderTypeByNumber", arg0, arg1)
	ret0, _ := ret[0].(*types.Header)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetHeaderTypeByNumber indicates an expected call of GetHeaderTypeByNumber.
func (mr *MockEthAPIMockRecorder) GetHeaderTypeByNumber(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetHeaderTypeByNumber", reflect.TypeOf((*MockEthAPI)(nil).GetHeaderTypeByNumber), arg0, arg1)
}
