// Code generated by MockGen. DO NOT EDIT.
// Source: ./contract_client.go

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	big "math/big"
	reflect "reflect"

	common "github.com/ethereum/go-ethereum/common"
	consensus "github.com/ethereum/go-ethereum/consensus"
	ctypes "github.com/ethereum/go-ethereum/consensus/clique/ctypes"
	core "github.com/ethereum/go-ethereum/core"
	state "github.com/ethereum/go-ethereum/core/state"
	types "github.com/ethereum/go-ethereum/core/types"
	gomock "github.com/golang/mock/gomock"
)

// MockContractClient is a mock of ContractClient interface.
type MockContractClient struct {
	ctrl     *gomock.Controller
	recorder *MockContractClientMockRecorder
}

// MockContractClientMockRecorder is the mock recorder for MockContractClient.
type MockContractClientMockRecorder struct {
	mock *MockContractClient
}

// NewMockContractClient creates a new mock instance.
func NewMockContractClient(ctrl *gomock.Controller) *MockContractClient {
	mock := &MockContractClient{ctrl: ctrl}
	mock.recorder = &MockContractClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockContractClient) EXPECT() *MockContractClientMockRecorder {
	return m.recorder
}

// CommitSpan mocks base method.
func (m *MockContractClient) CommitSpan(val common.Address, state *state.StateDB, header *types.Header, chain core.ChainContext, txs *[]*types.Transaction, receipts *[]*types.Receipt, receivedTxs *[]*types.Transaction, usedGas *uint64, mining bool, validatorBytes []byte) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CommitSpan", val, state, header, chain, txs, receipts, receivedTxs, usedGas, mining, validatorBytes)
	ret0, _ := ret[0].(error)
	return ret0
}

// CommitSpan indicates an expected call of CommitSpan.
func (mr *MockContractClientMockRecorder) CommitSpan(val, state, header, chain, txs, receipts, receivedTxs, usedGas, mining, validatorBytes interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CommitSpan", reflect.TypeOf((*MockContractClient)(nil).CommitSpan), val, state, header, chain, txs, receipts, receivedTxs, usedGas, mining, validatorBytes)
}

// DistributeToValidator mocks base method.
func (m *MockContractClient) DistributeToValidator(contract common.Address, amount *big.Int, validator common.Address, state *state.StateDB, header *types.Header, chain core.ChainContext, txs *[]*types.Transaction, receipts *[]*types.Receipt, receivedTxs *[]*types.Transaction, usedGas *uint64, mining bool) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DistributeToValidator", contract, amount, validator, state, header, chain, txs, receipts, receivedTxs, usedGas, mining)
	ret0, _ := ret[0].(error)
	return ret0
}

// DistributeToValidator indicates an expected call of DistributeToValidator.
func (mr *MockContractClientMockRecorder) DistributeToValidator(contract, amount, validator, state, header, chain, txs, receipts, receivedTxs, usedGas, mining interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DistributeToValidator", reflect.TypeOf((*MockContractClient)(nil).DistributeToValidator), contract, amount, validator, state, header, chain, txs, receipts, receivedTxs, usedGas, mining)
}

// GetCurrentSpan mocks base method.
func (m *MockContractClient) GetCurrentSpan(ctx context.Context, header *types.Header) (*big.Int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCurrentSpan", ctx, header)
	ret0, _ := ret[0].(*big.Int)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCurrentSpan indicates an expected call of GetCurrentSpan.
func (mr *MockContractClientMockRecorder) GetCurrentSpan(ctx, header interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCurrentSpan", reflect.TypeOf((*MockContractClient)(nil).GetCurrentSpan), ctx, header)
}

// GetCurrentValidators mocks base method.
func (m *MockContractClient) GetCurrentValidators(headerHash common.Hash, blockNumber *big.Int) ([]*ctypes.Validator, *ctypes.SystemContracts, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCurrentValidators", headerHash, blockNumber)
	ret0, _ := ret[0].([]*ctypes.Validator)
	ret1, _ := ret[1].(*ctypes.SystemContracts)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetCurrentValidators indicates an expected call of GetCurrentValidators.
func (mr *MockContractClientMockRecorder) GetCurrentValidators(headerHash, blockNumber interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCurrentValidators", reflect.TypeOf((*MockContractClient)(nil).GetCurrentValidators), headerHash, blockNumber)
}

// GetEligibleValidators mocks base method.
func (m *MockContractClient) GetEligibleValidators(headerHash common.Hash, blockNumber uint64) ([]*ctypes.Validator, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEligibleValidators", headerHash, blockNumber)
	ret0, _ := ret[0].([]*ctypes.Validator)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEligibleValidators indicates an expected call of GetEligibleValidators.
func (mr *MockContractClientMockRecorder) GetEligibleValidators(headerHash, blockNumber interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEligibleValidators", reflect.TypeOf((*MockContractClient)(nil).GetEligibleValidators), headerHash, blockNumber)
}

// GetKKUB mocks base method.
func (m *MockContractClient) GetKKUB(ctx context.Context, header *types.Header, stakeManager common.Address) (common.Address, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetKKUB", ctx, header, stakeManager)
	ret0, _ := ret[0].(common.Address)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetKKUB indicates an expected call of GetKKUB.
func (mr *MockContractClientMockRecorder) GetKKUB(ctx, header, stakeManager interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetKKUB", reflect.TypeOf((*MockContractClient)(nil).GetKKUB), ctx, header, stakeManager)
}

// GetNftContract mocks base method.
func (m *MockContractClient) GetNftContract(ctx context.Context, header *types.Header, stakeManager common.Address) (common.Address, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNftContract", ctx, header, stakeManager)
	ret0, _ := ret[0].(common.Address)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNftContract indicates an expected call of GetNftContract.
func (mr *MockContractClientMockRecorder) GetNftContract(ctx, header, stakeManager interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNftContract", reflect.TypeOf((*MockContractClient)(nil).GetNftContract), ctx, header, stakeManager)
}

// GetSlashEpochSize mocks base method.
func (m *MockContractClient) GetSlashEpochSize(ctx context.Context, header *types.Header, slashManager common.Address) (*big.Int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSlashEpochSize", ctx, header, slashManager)
	ret0, _ := ret[0].(*big.Int)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSlashEpochSize indicates an expected call of GetSlashEpochSize.
func (mr *MockContractClientMockRecorder) GetSlashEpochSize(ctx, header, slashManager interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSlashEpochSize", reflect.TypeOf((*MockContractClient)(nil).GetSlashEpochSize), ctx, header, slashManager)
}

// GetSlashThreshold mocks base method.
func (m *MockContractClient) GetSlashThreshold(ctx context.Context, header *types.Header, slashManager common.Address) (*big.Int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSlashThreshold", ctx, header, slashManager)
	ret0, _ := ret[0].(*big.Int)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSlashThreshold indicates an expected call of GetSlashThreshold.
func (mr *MockContractClientMockRecorder) GetSlashThreshold(ctx, header, slashManager interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSlashThreshold", reflect.TypeOf((*MockContractClient)(nil).GetSlashThreshold), ctx, header, slashManager)
}

// GetSoloSlashRate mocks base method.
func (m *MockContractClient) GetSoloSlashRate(ctx context.Context, header *types.Header, stakeManagerStorage common.Address) (*big.Int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSoloSlashRate", ctx, header, stakeManagerStorage)
	ret0, _ := ret[0].(*big.Int)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSoloSlashRate indicates an expected call of GetSoloSlashRate.
func (mr *MockContractClientMockRecorder) GetSoloSlashRate(ctx, header, stakeManagerStorage interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSoloSlashRate", reflect.TypeOf((*MockContractClient)(nil).GetSoloSlashRate), ctx, header, stakeManagerStorage)
}

// GetStakeManagerStorage mocks base method.
func (m *MockContractClient) GetStakeManagerStorage(ctx context.Context, header *types.Header) (common.Address, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetStakeManagerStorage", ctx, header)
	ret0, _ := ret[0].(common.Address)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetStakeManagerStorage indicates an expected call of GetStakeManagerStorage.
func (mr *MockContractClientMockRecorder) GetStakeManagerStorage(ctx, header interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetStakeManagerStorage", reflect.TypeOf((*MockContractClient)(nil).GetStakeManagerStorage), ctx, header)
}

// GetStakeManagerVault mocks base method.
func (m *MockContractClient) GetStakeManagerVault(ctx context.Context, header *types.Header, stakeManager common.Address) (common.Address, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetStakeManagerVault", ctx, header, stakeManager)
	ret0, _ := ret[0].(common.Address)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetStakeManagerVault indicates an expected call of GetStakeManagerVault.
func (mr *MockContractClientMockRecorder) GetStakeManagerVault(ctx, header, stakeManager interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetStakeManagerVault", reflect.TypeOf((*MockContractClient)(nil).GetStakeManagerVault), ctx, header, stakeManager)
}

// Inject mocks base method.
func (m *MockContractClient) Inject(val common.Address, signTxFn ctypes.SignerTxFn) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Inject", val, signTxFn)
}

// Inject indicates an expected call of Inject.
func (mr *MockContractClientMockRecorder) Inject(val, signTxFn interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Inject", reflect.TypeOf((*MockContractClient)(nil).Inject), val, signTxFn)
}

// IsSlashed mocks base method.
func (m *MockContractClient) IsSlashed(contract common.Address, chain consensus.ChainHeaderReader, signer common.Address, span *big.Int, header *types.Header) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsSlashed", contract, chain, signer, span, header)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsSlashed indicates an expected call of IsSlashed.
func (mr *MockContractClientMockRecorder) IsSlashed(contract, chain, signer, span, header interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsSlashed", reflect.TypeOf((*MockContractClient)(nil).IsSlashed), contract, chain, signer, span, header)
}

// SetSigner mocks base method.
func (m *MockContractClient) SetSigner(signer types.Signer) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SetSigner", signer)
}

// SetSigner indicates an expected call of SetSigner.
func (mr *MockContractClientMockRecorder) SetSigner(signer interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetSigner", reflect.TypeOf((*MockContractClient)(nil).SetSigner), signer)
}

// Slash mocks base method.
func (m *MockContractClient) Slash(contract, spoiledVal common.Address, chain consensus.ChainHeaderReader, state *state.StateDB, header *types.Header, cx core.ChainContext, txs *[]*types.Transaction, receipts *[]*types.Receipt, receivedTxs *[]*types.Transaction, usedGas *uint64, mining bool, currentSpan *big.Int) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Slash", contract, spoiledVal, chain, state, header, cx, txs, receipts, receivedTxs, usedGas, mining, currentSpan)
	ret0, _ := ret[0].(error)
	return ret0
}

// Slash indicates an expected call of Slash.
func (mr *MockContractClientMockRecorder) Slash(contract, spoiledVal, chain, state, header, cx, txs, receipts, receivedTxs, usedGas, mining, currentSpan interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Slash", reflect.TypeOf((*MockContractClient)(nil).Slash), contract, spoiledVal, chain, state, header, cx, txs, receipts, receivedTxs, usedGas, mining, currentSpan)
}
