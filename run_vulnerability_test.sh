#!/bin/bash

echo "========================================"
echo "CheckpointOracle Vulnerability Test"
echo "========================================"
echo

echo "Checking Go installation..."
if ! command -v go &> /dev/null; then
    echo "ERROR: Go is not installed or not in PATH"
    echo "Please install Go 1.19+ from https://golang.org/dl/"
    exit 1
fi

echo "Go is installed ✓"
echo

echo "Checking if we're in the correct directory..."
if [ ! -f "go.mod" ]; then
    echo "ERROR: go.mod not found"
    echo "Please run this script from the project root directory"
    exit 1
fi

echo "Project directory confirmed ✓"
echo

echo "Downloading dependencies..."
go mod tidy
if [ $? -ne 0 ]; then
    echo "ERROR: Failed to download dependencies"
    exit 1
fi

echo "Dependencies ready ✓"
echo

echo "========================================"
echo "Running Vulnerability Test..."
echo "========================================"
echo

echo "Option 1: Running standalone test script..."
echo
go run test_replay_vulnerability.go
if [ $? -ne 0 ]; then
    echo
    echo "Standalone test failed, trying integrated test..."
    echo
    echo "Option 2: Running integrated test..."
    go test -v ./contracts/checkpointoracle -run TestReplayProtectionBypass
fi

echo
echo "========================================"
echo "Test completed!"
echo "========================================"
echo
echo "If you see 'VULNERABILITY CONFIRMED', the issue exists."
echo "If you see 'Replay attack blocked', the issue is fixed."
echo
