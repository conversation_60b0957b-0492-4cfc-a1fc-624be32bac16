@echo off
echo ========================================
echo CheckpointOracle Vulnerability Test
echo ========================================
echo.

echo Checking Go installation...
go version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Go is not installed or not in PATH
    echo Please install Go 1.19+ from https://golang.org/dl/
    pause
    exit /b 1
)

echo Go is installed ✓
echo.

echo Checking if we're in the correct directory...
if not exist "go.mod" (
    echo ERROR: go.mod not found
    echo Please run this script from the project root directory
    pause
    exit /b 1
)

echo Project directory confirmed ✓
echo.

echo Downloading dependencies...
go mod tidy
if %errorlevel% neq 0 (
    echo ERROR: Failed to download dependencies
    pause
    exit /b 1
)

echo Dependencies ready ✓
echo.

echo ========================================
echo Running Vulnerability Test...
echo ========================================
echo.

echo Option 1: Running standalone test script...
echo.
go run test_replay_vulnerability.go
if %errorlevel% neq 0 (
    echo.
    echo Standalone test failed, trying integrated test...
    echo.
    echo Option 2: Running integrated test...
    go test -v ./contracts/checkpointoracle -run TestReplayProtectionBypass
)

echo.
echo ========================================
echo Test completed!
echo ========================================
echo.
echo If you see "VULNERABILITY CONFIRMED", the issue exists.
echo If you see "Replay attack blocked", the issue is fixed.
echo.
pause
