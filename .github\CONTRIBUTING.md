# Contributing

Thank you for considering to help out with the source code! We welcome 
contributions from anyone on the internet, and are grateful for even the 
smallest of fixes!

If you'd like to contribute to go-ethereum, please fork, fix, commit and send a 
pull request for the maintainers to review and merge into the main code base. If
you wish to submit more complex changes though, please check up with the core 
devs first on [our gitter channel](https://gitter.im/ethereum/go-ethereum) to 
ensure those changes are in line with the general philosophy of the project 
and/or get some early feedback which can make both your efforts much lighter as
well as our review and merge procedures quick and simple.

## Coding guidelines

Please make sure your contributions adhere to our coding guidelines:

 * Code must adhere to the official Go 
[formatting](https://golang.org/doc/effective_go.html#formatting) guidelines 
(i.e. uses [gofmt](https://golang.org/cmd/gofmt/)).
 * Code must be documented adhering to the official Go 
[commentary](https://golang.org/doc/effective_go.html#commentary) guidelines.
 * Pull requests need to be based on and opened against the `master` branch.
 * Commit messages should be prefixed with the package(s) they modify.
   * E.g. "eth, rpc: make trace configs optional"

## Can I have feature X

Before you submit a feature request, please check and make sure that it isn't 
possible through some other means. The JavaScript-enabled console is a powerful 
feature in the right hands. Please check our 
[Geth documentation page](https://geth.ethereum.org/docs/) for more info
and help.

## Configuration, dependencies, and tests

Please see the [Developers' Guide](https://geth.ethereum.org/docs/developers/devguide)
for more details on configuring your environment, managing project dependencies
and testing procedures.
