# CheckpointOracle Replay Protection Bypass - Vulnerability Test

This repository contains tests to demonstrate the **Critical** replay protection bypass vulnerability in the CheckpointOracle smart contract.

## Vulnerability Summary

**CVE**: Replay Protection Bypass via blockhash() Limitation  
**Severity**: Critical  
**Location**: `contracts/checkpointoracle/contract/oracle.sol` line 66  
**Issue**: `require(blockhash(_recentNumber) == _recentHash);`

The vulnerability allows attackers to replay old checkpoint submissions by exploiting the fact that `blockhash()` returns `0` for blocks older than 256 blocks.

## Prerequisites

1. **Go 1.19+** installed
2. **Git** to clone the repository
3. This is a **Go-Ethereum** project, so you need the go-ethereum dependencies

## Setup Instructions

### Option 1: Run the Existing Test (Recommended)

```bash
# Navigate to the project directory
cd /path/to/your/bkc/project

# Run the specific vulnerability test
go test -v ./contracts/checkpointoracle -run TestReplayProtectionBypass

# Or run all checkpoint oracle tests
go test -v ./contracts/checkpointoracle
```

### Option 2: Run the Standalone Test Script

```bash
# Navigate to the project directory
cd /path/to/your/bkc/project

# Run the standalone test script
go run test_replay_vulnerability.go
```

### Option 3: Manual Testing with Go Test

If you want to run just the vulnerability test:

```bash
# Create a simple test file
cat > vulnerability_test.go << 'EOF'
package main

import (
    "testing"
    "os"
)

func TestMain(m *testing.M) {
    // Run the test and exit
    code := m.Run()
    os.Exit(code)
}
EOF

# Run the test
go test -v -run TestReplayProtectionBypass ./contracts/checkpointoracle
```

## Expected Output

### If Vulnerability Exists (Current State):
```
=== CheckpointOracle Replay Protection Bypass Test ===
✅ Contract deployed at: 0x...
📝 Step 1: Submitting legitimate checkpoint...
   ✅ Checkpoint set successfully: index=0, height=123
⛏️  Step 2: Mining 260 blocks to make original block hash unavailable...
   📊 Current block: 383
   📊 Original block: 123 (now 260 blocks old)
🔓 Step 3: Attempting replay attack with zero hash...
   🚨 VULNERABILITY CONFIRMED: Replay attack succeeded!
   📝 Transaction hash: 0x...

🚨 CRITICAL VULNERABILITY CONFIRMED!
```

### If Vulnerability is Fixed:
```
🔓 Step 3: Attempting replay attack with zero hash...
   ✅ Replay attack blocked: execution reverted
   🛡️  The contract properly rejected the replay attempt
```

## Understanding the Test

The test performs these steps:

1. **Deploy Contract**: Sets up a CheckpointOracle with 3 admin signers and threshold of 2
2. **Submit Legitimate Checkpoint**: Creates a valid checkpoint with proper recent block data
3. **Mine 260 Blocks**: Advances the blockchain beyond the 256-block `blockhash()` limit
4. **Attempt Replay**: Tries to replay the same checkpoint with `_recentHash = 0x000...`
5. **Verify Result**: Checks if the replay was accepted (vulnerability) or rejected (secure)

## Technical Details

### The Vulnerability
```solidity
// Line 66 in oracle.sol - VULNERABLE CODE
require(blockhash(_recentNumber) == _recentHash);
```

### Why It Fails
- `blockhash()` only returns valid hashes for the most recent 256 blocks
- For older blocks: `blockhash(oldBlockNumber)` returns `0`
- Attacker sets `_recentHash = 0`
- Check becomes: `0 == 0` → `true` ✅ (bypassed!)

### Attack Scenario
1. Wait for legitimate checkpoint to become >256 blocks old
2. Replay the exact same transaction with `_recentHash = 0x000...`
3. Contract accepts it because `blockhash()` returns 0 for old blocks

## Recommended Fixes

### Fix 1: Nonce-Based Protection
```solidity
uint256 public checkpointNonce;

function SetCheckpoint(...) public {
    require(admins[msg.sender]);
    require(_nonce == checkpointNonce + 1);
    checkpointNonce = _nonce;
    // ... rest of function
}
```

### Fix 2: Time-Based Validation
```solidity
function SetCheckpoint(...) public {
    require(admins[msg.sender]);
    require(block.number - _recentNumber < 200, "Block too old");
    require(blockhash(_recentNumber) == _recentHash);
    // ... rest of function
}
```

### Fix 3: Combined Approach (Recommended)
```solidity
uint256 public lastProcessedBlock;

function SetCheckpoint(...) public {
    require(admins[msg.sender]);
    require(_recentNumber > lastProcessedBlock, "Block already processed");
    require(block.number - _recentNumber < 200, "Block too old");
    require(blockhash(_recentNumber) == _recentHash);
    
    lastProcessedBlock = _recentNumber;
    // ... rest of function
}
```

## Troubleshooting

### Common Issues

1. **Import Errors**: Make sure you're in the correct project directory with `go.mod`
2. **Missing Dependencies**: Run `go mod tidy` to download dependencies
3. **Build Errors**: Ensure you have Go 1.19+ installed

### Debug Mode
To see more detailed output, run with verbose flag:
```bash
go test -v -run TestReplayProtectionBypass ./contracts/checkpointoracle
```

## Security Impact

- **Severity**: Critical
- **CVSS Score**: 8.1 (High)
- **Impact**: Complete bypass of replay protection
- **Risk**: Attackers can replay old checkpoints, potentially overwriting legitimate data

## Contact

If you have questions about this vulnerability test, please refer to the original audit report or security documentation.
